import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider } from "@/components/ThemeProvider";
import { AuthProvider } from "@/contexts/AuthContext";
import { OrganizationProvider } from "@/contexts/OrganizationContext";
import BlogPromotionToast from "@/components/BlogPromotionToast";
import PWAInstallPrompt from "@/components/PWAInstallPrompt";
import AIAssistant from "@/components/AIAssistant";
import Index from "./pages/Index";
import Portfolio from "./pages/Portfolio";
import Testimonials from "./pages/Testimonials";
import CaseStudies from "./pages/CaseStudies";
import Blog from "./pages/Blog";
import BlogPost from "./pages/BlogPost";
import BlogDashboard from "./pages/admin/BlogDashboard";
import BlogEditor from "./pages/admin/BlogEditor";
import CreateTestimonial from "./pages/admin/CreateTestimonial";
import ProfileSettings from "./pages/admin/ProfileSettings";
import UserManagement from "./pages/admin/UserManagement";
import Changelog from "./pages/admin/Changelog";
import SystemSettings from "./pages/admin/SystemSettings";
import QuoteRequests from "./pages/admin/QuoteRequests";
import Automations from "./pages/admin/Automations";
import WorkflowEditor from "./pages/admin/WorkflowEditor";
import EmailIntegrations from "./pages/admin/EmailIntegrations";
import Analytics from "./pages/admin/Analytics";
import CreditsUsage from "./pages/admin/CreditsUsage";

import ModerationDashboard from "./pages/admin/ModerationDashboard";
import Pricing from "./pages/Pricing";
import PricingDemo from "./pages/PricingDemo";
import BillingSuccess from "./pages/BillingSuccess";
import BillingManagement from "./pages/BillingManagement";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsAndConditions from "./pages/TermsAndConditions";
import TestSlider from "./pages/TestSlider";
import NotFound from "./pages/NotFound";
import AuthDebug from "./components/debug/AuthDebug";
import RoleBasedRoute from "./components/auth/RoleBasedRoute";
import ResetPassword from "./pages/ResetPassword";
import AuthCallback from "./pages/AuthCallback";
import ErrorBoundary from "./components/ErrorBoundary";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="light" storageKey="mbi-ui-theme">
      <AuthProvider>
        <OrganizationProvider>
          <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <ErrorBoundary>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/portfolio" element={<Portfolio />} />
              <Route path="/testimonials" element={<Testimonials />} />
              <Route path="/case-studies" element={<CaseStudies />} />
              <Route path="/blog" element={<Blog />} />
              <Route path="/blog/:slug" element={<BlogPost />} />

              {/* Dashboard Routes (for regular users) */}
              <Route path="/dashboard" element={<RoleBasedRoute><BlogDashboard /></RoleBasedRoute>} />
              <Route path="/dashboard/blog" element={<RoleBasedRoute><BlogDashboard /></RoleBasedRoute>} />
              <Route path="/dashboard/blog/new" element={<RoleBasedRoute><BlogEditor /></RoleBasedRoute>} />
              <Route path="/dashboard/blog/edit/:id" element={<RoleBasedRoute><BlogEditor /></RoleBasedRoute>} />
              <Route path="/dashboard/testimonial" element={<RoleBasedRoute><CreateTestimonial /></RoleBasedRoute>} />
              <Route path="/dashboard/profile" element={<RoleBasedRoute><ProfileSettings /></RoleBasedRoute>} />
              <Route path="/dashboard/changelog" element={<RoleBasedRoute><Changelog /></RoleBasedRoute>} />
              <Route path="/dashboard/quotes" element={<RoleBasedRoute><QuoteRequests /></RoleBasedRoute>} />
              <Route path="/dashboard/automations" element={<RoleBasedRoute><Automations /></RoleBasedRoute>} />
              <Route path="/dashboard/automations/editor/:id" element={<RoleBasedRoute><WorkflowEditor /></RoleBasedRoute>} />
              <Route path="/dashboard/email-integrations" element={<RoleBasedRoute><EmailIntegrations /></RoleBasedRoute>} />
              <Route path="/dashboard/analytics" element={<RoleBasedRoute><Analytics /></RoleBasedRoute>} />
              <Route path="/dashboard/credits" element={<RoleBasedRoute><CreditsUsage /></RoleBasedRoute>} />

              {/* Admin Routes (for site administrators) */}
              <Route path="/admin/blog" element={<RoleBasedRoute adminOnly><BlogDashboard /></RoleBasedRoute>} />
              <Route path="/admin/blog/new" element={<RoleBasedRoute adminOnly><BlogEditor /></RoleBasedRoute>} />
              <Route path="/admin/blog/edit/:id" element={<RoleBasedRoute adminOnly><BlogEditor /></RoleBasedRoute>} />
              <Route path="/admin/blog/testimonial" element={<RoleBasedRoute adminOnly><CreateTestimonial /></RoleBasedRoute>} />
              <Route path="/admin/blog/profile" element={<RoleBasedRoute adminOnly><ProfileSettings /></RoleBasedRoute>} />
              <Route path="/admin/blog/users" element={<RoleBasedRoute adminOnly requiredRoles={['admin', 'super_admin', 'owner', 'saas_owner']}><UserManagement /></RoleBasedRoute>} />
              <Route path="/admin/blog/changelog" element={<RoleBasedRoute adminOnly><Changelog /></RoleBasedRoute>} />
              <Route path="/admin/quotes" element={<RoleBasedRoute adminOnly><QuoteRequests /></RoleBasedRoute>} />
              <Route path="/admin/automations" element={<RoleBasedRoute adminOnly><Automations /></RoleBasedRoute>} />
              <Route path="/admin/automations/editor/:id" element={<RoleBasedRoute adminOnly><WorkflowEditor /></RoleBasedRoute>} />
              <Route path="/admin/email-integrations" element={<RoleBasedRoute adminOnly><EmailIntegrations /></RoleBasedRoute>} />
              <Route path="/admin/analytics" element={<RoleBasedRoute adminOnly><Analytics /></RoleBasedRoute>} />
              <Route path="/admin/credits" element={<RoleBasedRoute adminOnly><CreditsUsage /></RoleBasedRoute>} />
              <Route path="/admin/moderation" element={<RoleBasedRoute adminOnly><ModerationDashboard /></RoleBasedRoute>} />
              <Route path="/admin/system-settings" element={<RoleBasedRoute adminOnly requiredRoles={['super_admin', 'owner', 'saas_owner']}><SystemSettings /></RoleBasedRoute>} />

              <Route path="/pricing" element={<Pricing />} />
              <Route path="/pricing-demo" element={<PricingDemo />} />
              <Route path="/billing/success" element={<BillingSuccess />} />
              <Route path="/billing" element={<BillingManagement />} />
              <Route path="/privacy-policy" element={<PrivacyPolicy />} />
              <Route path="/terms-and-conditions" element={<TermsAndConditions />} />
              <Route path="/test-slider" element={<TestSlider />} />
              <Route path="/debug/auth" element={<AuthDebug />} />

              {/* Password Reset Route */}
              <Route path="/reset-password" element={<ResetPassword />} />

              {/* OAuth Callback Route */}
              <Route path="/auth/callback" element={<AuthCallback />} />

              {/* Legacy redirects - redirect old admin routes to dashboard for regular users */}
              <Route path="/admin" element={<RoleBasedRoute adminOnly><Navigate to="/admin/blog" replace /></RoleBasedRoute>} />

              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
            {/* Blog Promotion Toast - appears on non-blog pages for non-authenticated users */}
            <BlogPromotionToast />
            {/* PWA Install Prompt */}
            <PWAInstallPrompt />
            {/* AI Assistant - appears on admin pages for authenticated users */}
            <AIAssistant />
            </ErrorBoundary>
          </BrowserRouter>
          </TooltipProvider>
        </OrganizationProvider>
      </AuthProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
