import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { useNavigate } from 'react-router-dom'
import { 
  Zap, 
  Users, 
  Mail, 
  Sparkles, 
  ArrowRight, 
  X,
  Crown,
  Rocket
} from 'lucide-react'

interface UpsellToastProps {
  trigger: 'workflow_limit' | 'ai_limit' | 'team_attempt' | 'email_limit'
  onDismiss?: () => void
}

const UPSELL_CONFIG = {
  workflow_limit: {
    icon: Zap,
    title: "Need More Workflow Credits?",
    message: "You've used your free credits. Upgrade for 10x more automation power!",
    benefits: ["1,000 workflow credits", "Advanced automation", "Email integration"],
    cta: "Upgrade to Basic - $10/month",
    highlight: "workflow_credits",
    color: "from-blue-500 to-purple-600"
  },
  ai_limit: {
    icon: Sparkles,
    title: "Need More AI Assistance?",
    message: "You've used your free AI requests. Upgrade for 10x more AI power!",
    benefits: ["500 AI requests/month", "Advanced AI models", "Blog generation"],
    cta: "Unlock AI Power - $10/month",
    highlight: "ai_credits",
    color: "from-purple-500 to-pink-600"
  },
  team_attempt: {
    icon: Users,
    title: "Team Collaboration Available! 👥",
    message: "Invite team members and collaborate on content & workflows!",
    benefits: ["3 team members", "Shared workspaces", "Collaboration tools"],
    cta: "Add Team Members - $10/month",
    highlight: "team_features",
    color: "from-green-500 to-teal-600"
  },
  email_limit: {
    icon: Mail,
    title: "Email Automation Ready! 📧",
    message: "Supercharge your email workflows with advanced automation!",
    benefits: ["Advanced email workflows", "Gmail integration", "Unlimited sends"],
    cta: "Boost Email Power - $10/month",
    highlight: "email_features",
    color: "from-orange-500 to-red-600"
  }
}

const UpsellToast: React.FC<UpsellToastProps> = ({ trigger, onDismiss }) => {
  const { user, profile } = useAuth()
  const navigate = useNavigate()
  const [dismissed, setDismissed] = useState(false)
  
  const config = UPSELL_CONFIG[trigger]
  const IconComponent = config.icon

  // Don't show if user is already on paid plan
  if (!user || !profile || profile.subscription_plan !== 'free' || dismissed) {
    return null
  }

  const handleUpgrade = () => {
    // Navigate to pricing page
    navigate('/pricing')
    setDismissed(true)
    onDismiss?.()
  }

  const handleDismiss = () => {
    setDismissed(true)
    onDismiss?.()
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      <div className={`bg-gradient-to-r ${config.color} p-1 rounded-lg shadow-2xl animate-in slide-in-from-bottom-4`}>
        <div className="bg-white dark:bg-gray-900 rounded-md p-4">
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className={`p-2 rounded-full bg-gradient-to-r ${config.color}`}>
                <IconComponent className="h-4 w-4 text-white" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
                {config.title}
              </h3>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Message */}
          <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
            {config.message}
          </p>

          {/* Benefits */}
          <div className="space-y-1 mb-4">
            {config.benefits.map((benefit, index) => (
              <div key={index} className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                <div className="h-1 w-1 bg-green-500 rounded-full" />
                {benefit}
              </div>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleUpgrade}
              className={`flex-1 bg-gradient-to-r ${config.color} hover:opacity-90 text-white text-xs`}
              size="sm"
            >
              <Crown className="h-3 w-3 mr-1" />
              Upgrade Now
              <ArrowRight className="h-3 w-3 ml-1" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDismiss}
              className="text-xs"
            >
              Later
            </Button>
          </div>

          {/* Trust indicator */}
          <div className="mt-2 text-center">
            <p className="text-xs text-gray-400">
              <Rocket className="h-3 w-3 inline mr-1" />
              Join 1000+ users automating their workflows
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Hook for triggering upsells
export const useUpsell = () => {
  const { user, profile } = useAuth()

  const triggerUpsell = (trigger: UpsellToastProps['trigger']) => {
    // Only show for free users
    if (!user || !profile || profile.subscription_plan !== 'free') {
      return
    }

    // Use sonner toast with custom component
    const config = UPSELL_CONFIG[trigger]
    
    toast.custom((t) => (
      <UpsellToast 
        trigger={trigger} 
        onDismiss={() => toast.dismiss(t)}
      />
    ), {
      duration: 10000, // 10 seconds
      position: 'bottom-right'
    })
  }

  const checkWorkflowLimit = (creditsUsed: number, creditsLimit: number) => {
    if (creditsUsed >= creditsLimit) {
      triggerUpsell('workflow_limit')
      return false
    }
    return true
  }

  const checkAiLimit = (aiCreditsUsed: number, aiCreditsLimit: number) => {
    if (aiCreditsUsed >= aiCreditsLimit) {
      triggerUpsell('ai_limit')
      return false
    }
    return true
  }

  const checkTeamLimit = () => {
    triggerUpsell('team_attempt')
    return false
  }

  const checkEmailLimit = () => {
    triggerUpsell('email_limit')
    return false
  }

  return {
    triggerUpsell,
    checkWorkflowLimit,
    checkAiLimit,
    checkTeamLimit,
    checkEmailLimit
  }
}

export default UpsellToast
