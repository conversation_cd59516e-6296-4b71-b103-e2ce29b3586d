import { useEffect, useRef, useState } from 'react'
import { supabase } from '@/lib/supabase'

interface TrackingOptions {
  postId: string
  organizationId: string
  userId?: string
}

interface DeviceInfo {
  deviceType: 'desktop' | 'mobile' | 'tablet'
  browser: string
  os: string
}

export const useAnalyticsTracking = ({ postId, organizationId, userId }: TrackingOptions) => {
  const [sessionId] = useState(() => {
    // Generate or get existing session ID
    let sessionId = sessionStorage.getItem('blog_session_id')
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('blog_session_id', sessionId)
    }
    return sessionId
  })

  const startTime = useRef<number>(Date.now())
  const scrollPercentage = useRef<number>(0)
  const hasTrackedView = useRef<boolean>(false)
  const readingTimeInterval = useRef<NodeJS.Timeout>()

  // Get device information
  const getDeviceInfo = (): DeviceInfo => {
    const userAgent = navigator.userAgent
    
    let deviceType: 'desktop' | 'mobile' | 'tablet' = 'desktop'
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      deviceType = 'tablet'
    } else if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
      deviceType = 'mobile'
    }

    let browser = 'Unknown'
    if (userAgent.includes('Chrome')) browser = 'Chrome'
    else if (userAgent.includes('Firefox')) browser = 'Firefox'
    else if (userAgent.includes('Safari')) browser = 'Safari'
    else if (userAgent.includes('Edge')) browser = 'Edge'

    let os = 'Unknown'
    if (userAgent.includes('Windows')) os = 'Windows'
    else if (userAgent.includes('Mac')) os = 'macOS'
    else if (userAgent.includes('Linux')) os = 'Linux'
    else if (userAgent.includes('Android')) os = 'Android'
    else if (userAgent.includes('iOS')) os = 'iOS'

    return { deviceType, browser, os }
  }

  // Track page view
  const trackView = async () => {
    if (hasTrackedView.current) return

    try {
      const deviceInfo = getDeviceInfo()
      
      await supabase.from('blog_post_views').insert({
        post_id: postId,
        organization_id: organizationId,
        user_id: userId || null,
        session_id: sessionId,
        referrer: document.referrer || null,
        device_type: deviceInfo.deviceType,
        browser: deviceInfo.browser,
        os: deviceInfo.os,
        user_agent: navigator.userAgent
      })

      hasTrackedView.current = true
    } catch (error) {
      console.error('Error tracking view:', error)
    }
  }

  // Track click events
  const trackClick = async (clickType: string = 'link', targetUrl?: string, elementId?: string) => {
    try {
      await supabase.from('blog_post_clicks').insert({
        post_id: postId,
        organization_id: organizationId,
        user_id: userId || null,
        session_id: sessionId,
        click_type: clickType,
        target_url: targetUrl,
        element_id: elementId
      })
    } catch (error) {
      console.error('Error tracking click:', error)
    }
  }

  // Track reading time and scroll
  const trackReadingTime = async (timeSpent: number, scrollPercent: number, isBounce: boolean = false) => {
    try {
      await supabase.from('blog_post_reading_time').upsert({
        post_id: postId,
        organization_id: organizationId,
        user_id: userId || null,
        session_id: sessionId,
        time_spent: Math.round(timeSpent / 1000), // Convert to seconds
        scroll_percentage: Math.round(scrollPercent),
        is_bounce: isBounce
      }, {
        onConflict: 'post_id,session_id'
      })
    } catch (error) {
      console.error('Error tracking reading time:', error)
    }
  }

  // Track reactions (likes, shares, etc.)
  const trackReaction = async (reactionType: 'like' | 'love' | 'share' | 'bookmark') => {
    if (!userId) return // Only authenticated users can react

    try {
      await supabase.from('blog_post_reactions').upsert({
        post_id: postId,
        organization_id: organizationId,
        user_id: userId,
        reaction_type: reactionType
      }, {
        onConflict: 'post_id,user_id,reaction_type'
      })
    } catch (error) {
      console.error('Error tracking reaction:', error)
    }
  }

  // Remove reaction
  const removeReaction = async (reactionType: 'like' | 'love' | 'share' | 'bookmark') => {
    if (!userId) return

    try {
      await supabase
        .from('blog_post_reactions')
        .delete()
        .match({
          post_id: postId,
          user_id: userId,
          reaction_type: reactionType
        })
    } catch (error) {
      console.error('Error removing reaction:', error)
    }
  }

  // Handle scroll tracking
  const handleScroll = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const scrollHeight = document.documentElement.scrollHeight - window.innerHeight
    const currentScrollPercentage = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0
    
    scrollPercentage.current = Math.max(scrollPercentage.current, currentScrollPercentage)
  }

  // Setup tracking
  useEffect(() => {
    // Track initial view
    trackView()

    // Setup scroll tracking
    window.addEventListener('scroll', handleScroll)

    // Setup reading time tracking (every 10 seconds)
    readingTimeInterval.current = setInterval(() => {
      const timeSpent = Date.now() - startTime.current
      const isBounce = timeSpent < 10000 && scrollPercentage.current < 25 // Less than 10s and 25% scroll
      
      trackReadingTime(timeSpent, scrollPercentage.current, isBounce)
    }, 10000)

    // Track on page unload
    const handleBeforeUnload = () => {
      const timeSpent = Date.now() - startTime.current
      const isBounce = timeSpent < 10000 && scrollPercentage.current < 25
      
      // Use sendBeacon for reliable tracking on page unload
      if (navigator.sendBeacon) {
        const data = JSON.stringify({
          post_id: postId,
          organization_id: organizationId,
          user_id: userId || null,
          session_id: sessionId,
          time_spent: Math.round(timeSpent / 1000),
          scroll_percentage: Math.round(scrollPercentage.current),
          is_bounce: isBounce
        })
        
        navigator.sendBeacon('/api/track-reading-time', data)
      } else {
        trackReadingTime(timeSpent, scrollPercentage.current, isBounce)
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('beforeunload', handleBeforeUnload)
      
      if (readingTimeInterval.current) {
        clearInterval(readingTimeInterval.current)
      }

      // Final reading time update
      const timeSpent = Date.now() - startTime.current
      const isBounce = timeSpent < 10000 && scrollPercentage.current < 25
      trackReadingTime(timeSpent, scrollPercentage.current, isBounce)
    }
  }, [postId, organizationId, userId])

  return {
    trackClick,
    trackReaction,
    removeReaction,
    sessionId
  }
}

// Hook for tracking link clicks automatically
export const useClickTracking = (trackingOptions: TrackingOptions) => {
  const { trackClick } = useAnalyticsTracking(trackingOptions)

  useEffect(() => {
    const handleClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      const link = target.closest('a')
      
      if (link) {
        const href = link.getAttribute('href')
        const id = link.getAttribute('id') || link.className
        
        trackClick('link', href || undefined, id || undefined)
      }
    }

    document.addEventListener('click', handleClick)
    
    return () => {
      document.removeEventListener('click', handleClick)
    }
  }, [trackClick])
}
