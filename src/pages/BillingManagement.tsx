import React, { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useOrganization } from '@/contexts/OrganizationContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import {
  CreditCard,
  Calendar,
  Settings,
  Download,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Loader2,
  RefreshCw
} from 'lucide-react'
import { toast } from 'sonner'
import { createCustomerPortalSession, hasActiveSubscription } from '@/lib/stripe'
import { SUBSCRIPTION_FEATURES } from '@/types/organization'

const BillingManagement = () => {
  const { profile } = useAuth()
  const { currentOrganization, refreshOrganization } = useOrganization()
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  const currentPlan = currentOrganization?.subscription_plan || profile?.subscription_plan || 'free'
  const subscriptionStatus = currentOrganization?.subscription_status || 'active'
  const isActive = hasActiveSubscription(subscriptionStatus)

  // Debug logging
  React.useEffect(() => {
    console.log('BillingManagement: Current subscription data:', {
      currentOrganization: currentOrganization ? {
        id: currentOrganization.id,
        subscription_plan: currentOrganization.subscription_plan,
        subscription_status: currentOrganization.subscription_status,
        stripe_customer_id: currentOrganization.stripe_customer_id,
        stripe_subscription_id: currentOrganization.stripe_subscription_id
      } : null,
      profile: profile ? {
        id: profile.id,
        subscription_plan: profile.subscription_plan
      } : null,
      computed: {
        currentPlan,
        subscriptionStatus,
        isActive
      }
    })
  }, [currentOrganization, profile, currentPlan, subscriptionStatus, isActive])

  // Auto-refresh organization data when component mounts (only if needed)
  React.useEffect(() => {
    if (currentOrganization && !currentOrganization.stripe_customer_id) {
      console.log('BillingManagement: Auto-refreshing organization data on mount (missing Stripe data)')
      refreshOrganization()
    }
  }, [currentOrganization?.id]) // Only run when organization changes

  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      await refreshOrganization()
      toast.success('Billing information refreshed')
    } catch (error) {
      toast.error('Failed to refresh billing information')
    } finally {
      setRefreshing(false)
    }
  }

  const handleManageBilling = async () => {
    if (!currentOrganization?.stripe_customer_id) {
      toast.error('No billing information found', {
        description: 'Please contact support if you believe this is an error.'
      })
      return
    }

    setLoading(true)
    try {
      const { url } = await createCustomerPortalSession(currentOrganization.stripe_customer_id)
      window.location.href = url
    } catch (error) {
      console.error('Error opening billing portal:', error)
      toast.error('Failed to open billing portal', {
        description: 'Please try again or contact support.'
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Active</Badge>
      case 'trialing':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Trial</Badge>
      case 'past_due':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Past Due</Badge>
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Cancelled</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const planDetails = SUBSCRIPTION_FEATURES[currentPlan as keyof typeof SUBSCRIPTION_FEATURES]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navigation />

      <div className="container mx-auto px-4 pt-24 pb-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="font-montserrat font-bold text-3xl text-gray-900 dark:text-white mb-2">
              Billing & Subscription
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Manage your subscription, billing information, and usage.
            </p>
          </div>

          {/* Current Plan Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Current Plan
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRefresh}
                    disabled={refreshing}
                    className="ml-2"
                  >
                    <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
                {getStatusBadge(subscriptionStatus)}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-lg mb-2">{planDetails?.name}</h3>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    ${planDetails?.price}/month
                  </p>
                  
                  {/* Plan Features */}
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Workflow Credits:</span>
                      <span className="font-semibold">
                        {planDetails?.workflowCredits === -1 ? 'Unlimited' : planDetails?.workflowCredits.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>AI Credits:</span>
                      <span className="font-semibold">
                        {planDetails?.aiCredits === -1 ? 'Unlimited' : planDetails?.aiCredits.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Team Members:</span>
                      <span className="font-semibold">
                        {planDetails?.maxTeamMembers === -1 ? 'Unlimited' : planDetails?.maxTeamMembers}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  {/* Billing Actions */}
                  {currentPlan !== 'free' && (
                    <Button 
                      onClick={handleManageBilling}
                      disabled={loading}
                      className="w-full"
                    >
                      {loading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Opening...
                        </>
                      ) : (
                        <>
                          <Settings className="h-4 w-4 mr-2" />
                          Manage Billing
                          <ExternalLink className="h-4 w-4 ml-2" />
                        </>
                      )}
                    </Button>
                  )}

                  {/* Upgrade Button for Free Plan */}
                  {currentPlan === 'free' && (
                    <Button 
                      onClick={() => window.location.href = '/pricing'}
                      className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                    >
                      Upgrade Plan
                    </Button>
                  )}

                  {/* Status Messages */}
                  {subscriptionStatus === 'past_due' && (
                    <div className="flex items-start gap-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                      <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
                      <div className="text-sm">
                        <p className="font-medium text-yellow-800 dark:text-yellow-200">Payment Past Due</p>
                        <p className="text-yellow-700 dark:text-yellow-300">
                          Please update your payment method to continue using premium features.
                        </p>
                      </div>
                    </div>
                  )}

                  {isActive && currentPlan !== 'free' && (
                    <div className="flex items-start gap-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                      <div className="text-sm">
                        <p className="font-medium text-green-800 dark:text-green-200">Subscription Active</p>
                        <p className="text-green-700 dark:text-green-300">
                          Your subscription is active and all features are available.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Usage Overview */}
          {currentOrganization && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Current Usage
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  {/* Workflow Credits */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">Workflow Credits</span>
                      <span className="text-sm text-gray-500">
                        {currentOrganization.workflow_credits_used} / {
                          currentOrganization.workflow_credits_limit === -1 
                            ? 'Unlimited' 
                            : currentOrganization.workflow_credits_limit.toLocaleString()
                        }
                      </span>
                    </div>
                    {currentOrganization.workflow_credits_limit !== -1 && (
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full" 
                          style={{ 
                            width: `${Math.min(
                              (currentOrganization.workflow_credits_used / currentOrganization.workflow_credits_limit) * 100, 
                              100
                            )}%` 
                          }}
                        />
                      </div>
                    )}
                  </div>

                  {/* AI Credits */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">AI Credits</span>
                      <span className="text-sm text-gray-500">
                        {currentOrganization.ai_credits_used} / {
                          currentOrganization.ai_credits_limit === -1 
                            ? 'Unlimited' 
                            : currentOrganization.ai_credits_limit.toLocaleString()
                        }
                      </span>
                    </div>
                    {currentOrganization.ai_credits_limit !== -1 && (
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-purple-500 h-2 rounded-full" 
                          style={{ 
                            width: `${Math.min(
                              (currentOrganization.ai_credits_used / currentOrganization.ai_credits_limit) * 100, 
                              100
                            )}%` 
                          }}
                        />
                      </div>
                    )}
                  </div>
                </div>

                {currentOrganization.credits_reset_date && (
                  <div className="mt-4 text-sm text-gray-500">
                    Credits reset on: {new Date(currentOrganization.credits_reset_date).toLocaleDateString()}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Billing Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Billing Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Access your billing portal to:
                </p>
                <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1 ml-4">
                  <li>• Update payment methods</li>
                  <li>• Download invoices</li>
                  <li>• View billing history</li>
                  <li>• Update billing address</li>
                  <li>• Cancel subscription</li>
                </ul>

                {currentPlan === 'free' ? (
                  <p className="text-sm text-gray-500 italic">
                    No billing information available for free plan.
                  </p>
                ) : (
                  <Button 
                    variant="outline" 
                    onClick={handleManageBilling}
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Loading...
                      </>
                    ) : (
                      <>
                        Open Billing Portal
                        <ExternalLink className="h-4 w-4 ml-2" />
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default BillingManagement
