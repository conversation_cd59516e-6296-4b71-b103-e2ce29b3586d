import React, { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useOrganization } from '@/contexts/OrganizationContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import AdminLayout from '@/components/layout/AdminLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent
} from '@/components/ui/chart'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import {
  BarChart3,
  Eye,
  MousePointer,
  Clock,
  TrendingUp,
  Users,
  Globe,
  Smartphone,
  Monitor,
  Tablet,
  ExternalLink,
  Calendar,
  ArrowUp,
  ArrowDown,
  Minus,
  MoreHorizontal,
  Copy,
  Edit,
  Trash2,
  Share,
  Settings
} from 'lucide-react'
import { toast } from 'sonner'
import { supabase } from '@/lib/supabase'

interface BlogAnalytics {
  post_id: string
  title: string
  slug: string
  view_count: number
  unique_view_count: number
  click_count: number
  avg_reading_time: number
  bounce_rate: number
  reaction_count: number
  comment_count: number
  share_count: number
  published_at: string
}

interface OverviewStats {
  totalViews: number
  totalUniqueViews: number
  totalClicks: number
  avgReadingTime: number
  avgBounceRate: number
  totalPosts: number
  publishedPosts: number
}

interface TrafficSource {
  referrer: string
  views: number
  percentage: number
}

interface DeviceStats {
  device_type: string
  views: number
  percentage: number
}

interface ChartData {
  date: string
  views: number
  uniqueViews: number
  clicks: number
  readingTime: number
}

interface TopPost {
  id: string
  title: string
  slug: string
  views: number
  clicks: number
  readingTime: number
  bounceRate: number
}

const Analytics = () => {
  const { profile } = useAuth()
  const { currentOrganization } = useOrganization()
  const [analytics, setAnalytics] = useState<BlogAnalytics[]>([])
  const [overviewStats, setOverviewStats] = useState<OverviewStats>({
    totalViews: 0,
    totalUniqueViews: 0,
    totalClicks: 0,
    avgReadingTime: 0,
    avgBounceRate: 0,
    totalPosts: 0,
    publishedPosts: 0
  })
  const [trafficSources, setTrafficSources] = useState<TrafficSource[]>([])
  const [deviceStats, setDeviceStats] = useState<DeviceStats[]>([])
  const [chartData, setChartData] = useState<ChartData[]>([])
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')
  const [sortBy, setSortBy] = useState('view_count')

  // Sample data for testing when no real data exists
  const sampleChartData: ChartData[] = [
    { date: 'Jan 1', views: 120, uniqueViews: 80, clicks: 15, readingTime: 180 },
    { date: 'Jan 2', views: 150, uniqueViews: 95, clicks: 22, readingTime: 165 },
    { date: 'Jan 3', views: 180, uniqueViews: 110, clicks: 28, readingTime: 200 },
    { date: 'Jan 4', views: 140, uniqueViews: 85, clicks: 18, readingTime: 175 },
    { date: 'Jan 5', views: 200, uniqueViews: 130, clicks: 35, readingTime: 220 },
    { date: 'Jan 6', views: 170, uniqueViews: 105, clicks: 25, readingTime: 190 },
    { date: 'Jan 7', views: 190, uniqueViews: 120, clicks: 30, readingTime: 210 },
  ]

  const sampleTrafficSources: TrafficSource[] = [
    { referrer: 'google.com', views: 450, percentage: 45 },
    { referrer: 'twitter.com', views: 250, percentage: 25 },
    { referrer: 'linkedin.com', views: 150, percentage: 15 },
    { referrer: 'Direct', views: 100, percentage: 10 },
    { referrer: 'facebook.com', views: 50, percentage: 5 },
  ]

  const sampleDeviceStats: DeviceStats[] = [
    { device_type: 'desktop', views: 600, percentage: 60 },
    { device_type: 'mobile', views: 300, percentage: 30 },
    { device_type: 'tablet', views: 100, percentage: 10 },
  ]

  const sampleBlogPosts: BlogAnalytics[] = [
    {
      post_id: '1',
      title: 'Getting Started with React and TypeScript',
      slug: 'getting-started-react-typescript',
      view_count: 1250,
      unique_view_count: 890,
      click_count: 125,
      avg_reading_time: 240,
      bounce_rate: 25.5,
      reaction_count: 45,
      comment_count: 12,
      share_count: 8,
      published_at: '2024-01-15T10:00:00Z'
    },
    {
      post_id: '2',
      title: 'Building Modern Web Applications with Next.js',
      slug: 'building-modern-web-apps-nextjs',
      view_count: 980,
      unique_view_count: 720,
      click_count: 95,
      avg_reading_time: 320,
      bounce_rate: 30.2,
      reaction_count: 38,
      comment_count: 9,
      share_count: 6,
      published_at: '2024-01-12T14:30:00Z'
    },
    {
      post_id: '3',
      title: 'Advanced CSS Techniques for Better UX',
      slug: 'advanced-css-techniques-better-ux',
      view_count: 750,
      unique_view_count: 580,
      click_count: 68,
      avg_reading_time: 180,
      bounce_rate: 40.1,
      reaction_count: 29,
      comment_count: 7,
      share_count: 4,
      published_at: '2024-01-10T09:15:00Z'
    },
    {
      post_id: '4',
      title: 'Database Design Best Practices',
      slug: 'database-design-best-practices',
      view_count: 650,
      unique_view_count: 480,
      click_count: 52,
      avg_reading_time: 280,
      bounce_rate: 35.8,
      reaction_count: 22,
      comment_count: 5,
      share_count: 3,
      published_at: '2024-01-08T16:45:00Z'
    },
    {
      post_id: '5',
      title: 'API Security: Protecting Your Endpoints',
      slug: 'api-security-protecting-endpoints',
      view_count: 520,
      unique_view_count: 390,
      click_count: 41,
      avg_reading_time: 220,
      bounce_rate: 45.3,
      reaction_count: 18,
      comment_count: 4,
      share_count: 2,
      published_at: '2024-01-05T11:20:00Z'
    }
  ]

  useEffect(() => {
    loadAnalytics()
  }, [currentOrganization, timeRange])

  const loadAnalytics = async () => {
    if (!currentOrganization) return

    setLoading(true)
    try {
      // Calculate date range
      const endDate = new Date()
      const startDate = new Date()
      switch (timeRange) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7)
          break
        case '30d':
          startDate.setDate(endDate.getDate() - 30)
          break
        case '90d':
          startDate.setDate(endDate.getDate() - 90)
          break
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1)
          break
      }

      // Determine organization filter based on user role
      const orgFilter = (profile?.role === 'saas_owner' || profile?.role === 'super_admin') 
        ? {} 
        : { organization_id: currentOrganization.id }

      // Load blog post analytics
      const { data: blogData, error: blogError } = await supabase
        .from('blog_posts')
        .select(`
          id,
          title,
          slug,
          view_count,
          unique_view_count,
          click_count,
          avg_reading_time,
          bounce_rate,
          reaction_count,
          comment_count,
          share_count,
          published_at
        `)
        .match(orgFilter)
        .eq('status', 'published')
        .gte('published_at', startDate.toISOString())
        .order(sortBy, { ascending: false })

      if (blogError) throw blogError

      setAnalytics(blogData || [])

      // Calculate overview stats
      const stats = (blogData || []).reduce((acc, post) => ({
        totalViews: acc.totalViews + (post.view_count || 0),
        totalUniqueViews: acc.totalUniqueViews + (post.unique_view_count || 0),
        totalClicks: acc.totalClicks + (post.click_count || 0),
        avgReadingTime: acc.avgReadingTime + (post.avg_reading_time || 0),
        avgBounceRate: acc.avgBounceRate + (post.bounce_rate || 0),
        totalPosts: acc.totalPosts + 1,
        publishedPosts: acc.publishedPosts + 1
      }), {
        totalViews: 0,
        totalUniqueViews: 0,
        totalClicks: 0,
        avgReadingTime: 0,
        avgBounceRate: 0,
        totalPosts: 0,
        publishedPosts: 0
      })

      if (stats.totalPosts > 0) {
        stats.avgReadingTime = Math.round(stats.avgReadingTime / stats.totalPosts)
        stats.avgBounceRate = Math.round((stats.avgBounceRate / stats.totalPosts) * 100) / 100
      }

      setOverviewStats(stats)

      // Load traffic sources
      const { data: trafficData, error: trafficError } = await supabase
        .from('blog_post_views')
        .select('referrer')
        .gte('created_at', startDate.toISOString())
        .not('referrer', 'is', null)

      if (!trafficError && trafficData) {
        const referrerCounts = trafficData.reduce((acc: Record<string, number>, view) => {
          const referrer = view.referrer || 'Direct'
          acc[referrer] = (acc[referrer] || 0) + 1
          return acc
        }, {})

        const totalTraffic = Object.values(referrerCounts).reduce((sum: number, count) => sum + count, 0)
        const sources = Object.entries(referrerCounts)
          .map(([referrer, views]) => ({
            referrer: referrer.replace(/^https?:\/\//, '').split('/')[0] || 'Direct',
            views: views as number,
            percentage: Math.round((views as number / totalTraffic) * 100)
          }))
          .sort((a, b) => b.views - a.views)
          .slice(0, 5)

        setTrafficSources(sources)
      }

      // Load device stats
      const { data: deviceData, error: deviceError } = await supabase
        .from('blog_post_views')
        .select('device_type')
        .gte('created_at', startDate.toISOString())
        .not('device_type', 'is', null)

      if (!deviceError && deviceData) {
        const deviceCounts = deviceData.reduce((acc: Record<string, number>, view) => {
          const device = view.device_type || 'Unknown'
          acc[device] = (acc[device] || 0) + 1
          return acc
        }, {})

        const totalDeviceViews = Object.values(deviceCounts).reduce((sum: number, count) => sum + count, 0)
        const devices = Object.entries(deviceCounts)
          .map(([device_type, views]) => ({
            device_type,
            views: views as number,
            percentage: Math.round((views as number / totalDeviceViews) * 100)
          }))
          .sort((a, b) => b.views - a.views)

        setDeviceStats(devices)
      }

      // Generate chart data for the time range
      const chartDataPoints: ChartData[] = []
      const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        const dateStr = date.toISOString().split('T')[0]

        // Get views for this date
        const { data: dayViews } = await supabase
          .from('blog_post_views')
          .select('post_id, created_at')
          .gte('created_at', `${dateStr}T00:00:00.000Z`)
          .lt('created_at', `${dateStr}T23:59:59.999Z`)

        // Get clicks for this date
        const { data: dayClicks } = await supabase
          .from('blog_post_clicks')
          .select('post_id, created_at')
          .gte('created_at', `${dateStr}T00:00:00.000Z`)
          .lt('created_at', `${dateStr}T23:59:59.999Z`)

        // Get reading time for this date
        const { data: dayReadingTime } = await supabase
          .from('blog_post_reading_time')
          .select('time_spent, created_at')
          .gte('created_at', `${dateStr}T00:00:00.000Z`)
          .lt('created_at', `${dateStr}T23:59:59.999Z`)

        const uniqueViews = new Set(dayViews?.map(v => v.post_id) || []).size
        const avgReadingTime = dayReadingTime?.length
          ? Math.round(dayReadingTime.reduce((sum, r) => sum + r.time_spent, 0) / dayReadingTime.length)
          : 0

        chartDataPoints.push({
          date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          views: dayViews?.length || 0,
          uniqueViews,
          clicks: dayClicks?.length || 0,
          readingTime: avgReadingTime
        })
      }

      setChartData(chartDataPoints)

      // If no real data, use sample data for demonstration
      if (chartDataPoints.length === 0 || chartDataPoints.every(point => point.views === 0)) {
        setChartData(sampleChartData)
      }
      if (trafficSources.length === 0) {
        setTrafficSources(sampleTrafficSources)
      }
      if (deviceStats.length === 0) {
        setDeviceStats(sampleDeviceStats)
      }
      if (!blogData || blogData.length === 0) {
        setAnalytics(sampleBlogPosts)
        setOverviewStats({
          totalViews: 4150,
          totalUniqueViews: 3060,
          totalClicks: 381,
          avgReadingTime: 248,
          avgBounceRate: 35.4,
          totalPosts: 5,
          publishedPosts: 5
        })
      }

    } catch (error) {
      console.error('Error loading analytics:', error)
      toast.error('Failed to load analytics data')

      // Use sample data on error for demonstration
      setChartData(sampleChartData)
      setTrafficSources(sampleTrafficSources)
      setDeviceStats(sampleDeviceStats)
      setOverviewStats({
        totalViews: 1000,
        totalUniqueViews: 650,
        totalClicks: 150,
        avgReadingTime: 185,
        avgBounceRate: 35.5,
        totalPosts: 12,
        publishedPosts: 8
      })
    } finally {
      setLoading(false)
    }
  }

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const getPerformanceColor = (value: number, type: 'bounce' | 'ctr' | 'time') => {
    switch (type) {
      case 'bounce':
        if (value > 70) return 'text-red-600'
        if (value > 50) return 'text-yellow-600'
        return 'text-green-600'
      case 'ctr':
        if (value > 5) return 'text-green-600'
        if (value > 2) return 'text-yellow-600'
        return 'text-red-600'
      case 'time':
        if (value > 120) return 'text-green-600'
        if (value > 60) return 'text-yellow-600'
        return 'text-red-600'
      default:
        return 'text-muted-foreground'
    }
  }

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType.toLowerCase()) {
      case 'mobile': return Smartphone
      case 'tablet': return Tablet
      case 'desktop': return Monitor
      default: return Monitor
    }
  }

  const getTrendIcon = (value: number, threshold: number = 0) => {
    if (value > threshold) return <ArrowUp className="h-4 w-4 text-green-500" />
    if (value < threshold) return <ArrowDown className="h-4 w-4 text-red-500" />
    return <Minus className="h-4 w-4 text-gray-500" />
  }

  // Chart colors
  const chartColors = {
    primary: '#3b82f6',
    secondary: '#10b981',
    accent: '#f59e0b',
    muted: '#6b7280'
  }

  const deviceColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6']

  return (
    <ProtectedRoute requiredRole={['user', 'admin', 'super_admin', 'owner', 'saas_owner']}>
      <AdminLayout
        title="Blog Analytics"
        subtitle="Track your blog performance and audience engagement"
        actions={
          <div className="flex gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={loadAnalytics} variant="outline">
              <BarChart3 className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        }
      >
        {loading ? (
          <div className="space-y-6">
            {/* Loading Skeleton for Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div className="h-4 bg-muted rounded w-20"></div>
                    <div className="h-4 w-4 bg-muted rounded"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-8 bg-muted rounded w-16 mb-2"></div>
                    <div className="h-3 bg-muted rounded w-24"></div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Loading Skeleton for Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {[1, 2].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-6 bg-muted rounded w-32"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80 bg-muted rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Loading Skeleton for Posts List */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card className="animate-pulse">
                  <CardHeader>
                    <div className="h-6 bg-muted rounded w-40"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[1, 2, 3, 4, 5].map((i) => (
                        <div key={i} className="p-4 border rounded-lg">
                          <div className="h-5 bg-muted rounded w-3/4 mb-2"></div>
                          <div className="h-4 bg-muted rounded w-1/2"></div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
              <div className="space-y-6">
                {[1, 2].map((i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-6 bg-muted rounded w-32"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-32 bg-muted rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500 bg-gradient-to-br from-blue-50 to-background dark:from-blue-950/20">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Total Views</CardTitle>
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                    <Eye className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {overviewStats.totalViews.toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {overviewStats.totalUniqueViews.toLocaleString()} unique visitors
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-amber-500 bg-gradient-to-br from-amber-50 to-background dark:from-amber-950/20">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-amber-700 dark:text-amber-300">Total Clicks</CardTitle>
                  <div className="p-2 bg-amber-100 dark:bg-amber-900/30 rounded-full">
                    <MousePointer className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-amber-900 dark:text-amber-100">
                    {overviewStats.totalClicks.toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    {overviewStats.totalViews > 0
                      ? `${Math.round((overviewStats.totalClicks / overviewStats.totalViews) * 100)}% CTR`
                      : '0% CTR'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-purple-500 bg-gradient-to-br from-purple-50 to-background dark:from-purple-950/20">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">Avg. Reading Time</CardTitle>
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                    <Clock className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {formatDuration(overviewStats.avgReadingTime)}
                  </div>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <ArrowDown className="h-3 w-3" />
                    {overviewStats.avgBounceRate.toFixed(1)}% bounce rate
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-green-500 bg-gradient-to-br from-green-50 to-background dark:from-green-950/20">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">Published Posts</CardTitle>
                  <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-full">
                    <TrendingUp className="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {overviewStats.publishedPosts}
                  </div>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    in selected period
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Interactive Charts Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* Traffic Overview Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Traffic Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ChartContainer
                    config={{
                      views: {
                        label: "Total Views",
                        color: "#3b82f6",
                      },
                      uniqueViews: {
                        label: "Unique Views",
                        color: "#10b981",
                      },
                    }}
                    className="h-80"
                  >
                    <AreaChart data={chartData}>
                      <defs>
                        <linearGradient id="colorViews" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                          <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                        </linearGradient>
                        <linearGradient id="colorUniqueViews" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                          <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                      <XAxis
                        dataKey="date"
                        axisLine={false}
                        tickLine={false}
                        className="text-xs"
                      />
                      <YAxis
                        axisLine={false}
                        tickLine={false}
                        className="text-xs"
                      />
                      <ChartTooltip
                        content={<ChartTooltipContent />}
                        cursor={{ stroke: '#3b82f6', strokeWidth: 1, strokeDasharray: '3 3' }}
                      />
                      <Area
                        type="monotone"
                        dataKey="views"
                        stroke="#3b82f6"
                        strokeWidth={2}
                        fillOpacity={1}
                        fill="url(#colorViews)"
                        name="Total Views"
                      />
                      <Area
                        type="monotone"
                        dataKey="uniqueViews"
                        stroke="#10b981"
                        strokeWidth={2}
                        fillOpacity={1}
                        fill="url(#colorUniqueViews)"
                        name="Unique Views"
                      />
                    </AreaChart>
                  </ChartContainer>
                </CardContent>
              </Card>

              {/* Engagement Metrics Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Engagement Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ChartContainer
                    config={{
                      clicks: {
                        label: "Clicks",
                        color: "#f59e0b",
                      },
                      readingTime: {
                        label: "Avg Reading Time (s)",
                        color: "#8b5cf6",
                      },
                    }}
                    className="h-80"
                  >
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                      <XAxis
                        dataKey="date"
                        axisLine={false}
                        tickLine={false}
                        className="text-xs"
                      />
                      <YAxis
                        yAxisId="clicks"
                        orientation="left"
                        axisLine={false}
                        tickLine={false}
                        className="text-xs"
                      />
                      <YAxis
                        yAxisId="time"
                        orientation="right"
                        axisLine={false}
                        tickLine={false}
                        className="text-xs"
                      />
                      <ChartTooltip
                        content={<ChartTooltipContent />}
                        cursor={{ stroke: '#8b5cf6', strokeWidth: 1, strokeDasharray: '3 3' }}
                      />
                      <Line
                        yAxisId="clicks"
                        type="monotone"
                        dataKey="clicks"
                        stroke="#f59e0b"
                        strokeWidth={3}
                        dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: '#f59e0b', strokeWidth: 2 }}
                        name="Clicks"
                      />
                      <Line
                        yAxisId="time"
                        type="monotone"
                        dataKey="readingTime"
                        stroke="#8b5cf6"
                        strokeWidth={3}
                        dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: '#8b5cf6', strokeWidth: 2 }}
                        name="Avg Reading Time (s)"
                      />
                    </LineChart>
                  </ChartContainer>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Top Performing Posts */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Top Performing Posts
                    </CardTitle>
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="view_count">Views</SelectItem>
                        <SelectItem value="unique_view_count">Unique Views</SelectItem>
                        <SelectItem value="click_count">Clicks</SelectItem>
                        <SelectItem value="avg_reading_time">Reading Time</SelectItem>
                        <SelectItem value="reaction_count">Reactions</SelectItem>
                      </SelectContent>
                    </Select>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analytics.slice(0, 10).map((post, index) => {
                        const maxValue = Math.max(...analytics.slice(0, 10).map(p => p[sortBy as keyof BlogAnalytics] as number || 0))
                        const currentValue = (post[sortBy as keyof BlogAnalytics] as number) || 0
                        const percentage = maxValue > 0 ? (currentValue / maxValue) * 100 : 0

                        return (
                          <div key={post.post_id} className="group relative">
                            <div className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-all duration-200 bg-gradient-to-r from-background to-muted/20">
                              {/* Ranking Badge */}
                              <div className="flex items-center gap-3">
                                <div className={`
                                  flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold
                                  ${index === 0 ? 'bg-yellow-500 text-white' :
                                    index === 1 ? 'bg-gray-400 text-white' :
                                    index === 2 ? 'bg-amber-600 text-white' :
                                    'bg-muted text-muted-foreground'}
                                `}>
                                  {index + 1}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <h4 className="font-semibold truncate group-hover:text-primary transition-colors">
                                    {post.title}
                                  </h4>
                                  <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                                    <span className="flex items-center gap-1">
                                      <Eye className="h-3 w-3" />
                                      {post.view_count?.toLocaleString() || 0}
                                    </span>
                                    <span className="flex items-center gap-1">
                                      <Users className="h-3 w-3" />
                                      {post.unique_view_count?.toLocaleString() || 0}
                                    </span>
                                    <span className="flex items-center gap-1">
                                      <MousePointer className="h-3 w-3" />
                                      {post.click_count?.toLocaleString() || 0}
                                    </span>
                                    <span className="flex items-center gap-1">
                                      <Clock className="h-3 w-3" />
                                      {formatDuration(post.avg_reading_time || 0)}
                                    </span>
                                  </div>
                                  {/* Performance Bar */}
                                  <div className="w-full bg-muted rounded-full h-1.5 mt-2">
                                    <div
                                      className="h-1.5 rounded-full transition-all duration-500 ease-out bg-gradient-to-r from-blue-500 to-purple-500"
                                      style={{ width: `${percentage}%` }}
                                    />
                                  </div>
                                </div>
                              </div>

                              <div className="flex items-center gap-2">
                                <div className="text-right">
                                  <div className="text-lg font-bold text-primary">
                                    {typeof currentValue === 'number' ? currentValue.toLocaleString() : currentValue}
                                  </div>
                                  <Badge variant={post.bounce_rate > 70 ? "destructive" : post.bounce_rate > 50 ? "secondary" : "default"}>
                                    {post.bounce_rate?.toFixed(1) || 0}% bounce
                                  </Badge>
                                </div>
                                <Button variant="ghost" size="sm" asChild className="opacity-0 group-hover:opacity-100 transition-opacity">
                                  <a href={`/blog/${post.slug}`} target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="h-4 w-4" />
                                  </a>
                                </Button>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Traffic Sources & Device Stats */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      Traffic Sources
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer
                      config={{
                        views: {
                          label: "Views",
                        },
                      }}
                      className="h-64 mb-4"
                    >
                      <PieChart>
                        <Pie
                          data={trafficSources.map((source, index) => ({
                            name: source.referrer,
                            value: source.views,
                            percentage: source.percentage
                          }))}
                          cx="50%"
                          cy="50%"
                          innerRadius={40}
                          outerRadius={80}
                          paddingAngle={2}
                          dataKey="value"
                        >
                          {trafficSources.map((_, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={[
                                '#3b82f6', '#10b981', '#f59e0b', '#ef4444',
                                '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
                              ][index % 8]}
                            />
                          ))}
                        </Pie>
                        <ChartTooltip
                          content={({ active, payload }) => {
                            if (active && payload && payload.length) {
                              const data = payload[0].payload
                              return (
                                <div className="bg-background border rounded-lg p-3 shadow-lg">
                                  <p className="font-medium">{data.name}</p>
                                  <p className="text-sm text-muted-foreground">
                                    {data.value} views ({data.percentage}%)
                                  </p>
                                </div>
                              )
                            }
                            return null
                          }}
                        />
                      </PieChart>
                    </ChartContainer>
                    <div className="space-y-2">
                      {trafficSources.slice(0, 5).map((source, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{
                                backgroundColor: [
                                  '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'
                                ][index]
                              }}
                            />
                            <span className="text-sm truncate flex-1">{source.referrer}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">{source.views}</span>
                            <Badge variant="outline">{source.percentage}%</Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Monitor className="h-4 w-4" />
                      Device Types
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {deviceStats.map((device, index) => {
                        const DeviceIcon = getDeviceIcon(device.device_type)
                        const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444']
                        return (
                          <div key={index} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <DeviceIcon className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm capitalize font-medium">{device.device_type}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">{device.views}</span>
                                <Badge variant="outline">{device.percentage}%</Badge>
                              </div>
                            </div>
                            <div className="w-full bg-muted rounded-full h-2">
                              <div
                                className="h-2 rounded-full transition-all duration-500 ease-out"
                                style={{
                                  width: `${device.percentage}%`,
                                  backgroundColor: colors[index % colors.length]
                                }}
                              />
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}
      </AdminLayout>
    </ProtectedRoute>
  )
}

export default Analytics
